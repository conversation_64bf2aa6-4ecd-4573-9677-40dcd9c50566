# ✅ Final Instruction Set 
**Goal:** Generate a Markdown-formatted Experiment Details Report using the provided files.

---

## 📁 Files Provided

| File                          | Purpose |
|-------------------------------|---------|
| `Experiment_details.pdf`      | Reference format for final report structure |
| `pause-doc.pdf`               | Technical background, motivations, terminology |
| `wandb_experiments_14_40.csv` | Raw configuration data for each experiment run |

---

## 🧠 Background Knowledge

- **Two-Stage Training:**
  - **Stage 1 (Latent Space):** Must be trained before Stage 2. Fewer losses, faster iterations.
  - **Stage 2 (Pixel Space):** Uses full loss setup. Requires Stage 1 checkpoint.

- **Affine Version:**
  - If config contains `"face_template"` → New affine
  - If not → Old affine

- **Important:**  
  Do **not** mention **Latentsync** anywhere in the final report, even if present in configs.  
  It may be used internally for reasoning but should not appear in any section of the report.

---

## 📌 Report Requirements

### ✅ Format: Markdown Only

Each experiment must follow this exact format:

```markdown
## Exp-{x}: [Descriptive Title]

**Description:**  
[1–3 sentences explaining the changes made.]

**Approach:**  
[How the experiment was implemented. Optional.]

**Aim:**  
[What this experiment was trying to verify or discover.]

**Key Learnings:**  
[Only if outcome is clear or can be reasonably inferred.]

**Status:**  
[Success / Failed / Crashed / Continued from Exp-x]


⸻

🔄 Experiment Naming Rules
	•	All experiments are named like Exp-1, Exp-2, etc.
	•	You must strictly sort experiments by Exp-{x}.
	•	If Exp-x appears more than once:
	•	Check number of training steps and other settings.
	•	If steps are increased → likely a continuation.
	•	➜ Mark: Status: Continued from Exp-x
	•	➜ State in description why it was continued (e.g., stability check)
	•	If identical settings → possibly a rerun
	•	➜ Mark: Status: Rerun of Exp-x
	•	➜ Confirm with human if unsure.
	•	If the experiment failed (training did not complete), mark:
	•	➜ Status: Failed

⸻

📑 How to Interpret Each File

1. Experiment_details.pdf (Structure Reference)
	•	Follow style strictly: title, description, approach, aim, and optional key learnings.
	•	Look at bullet structure when variants are tested in one experiment.

2. pause-doc.pdf (Concept Dictionary)

Use this for:
	•	Technical accuracy (e.g., “temporal coherency” instead of “smoothness”)
	•	Descriptions of challenges, solutions, and key architectural ideas like:
	•	SyncNet
	•	Affine Transformation
	•	ROI masking
	•	TREPA Loss
	•	Temporal Conditioning
	•	Face angle handling

3. wandb_experiments_14_40.csv (Raw Experiment Data)

Read each row to extract:
	•	exp_name, run_name
	•	Loss weights (syncnet, perceptual, trepa, recon, etc.)
	•	mask_type, inpainting_mask
	•	face_template → (New vs Old Affine)
	•	lr, scheduler, num_train_steps
	•	temporal_conditioning
	•	Stage indicator (latent vs pixel training)
	•	log_status, crash indicators → (use for Failed label)

⸻

🛠 Execution Guidelines

✅ Every experiment row must be included.
	•	Even if it’s incomplete or failed — mark clearly.

✅ Be precise. Don’t guess.
	•	If unsure whether it’s a rerun, continuation, or a variant:
	•	Formulate a question.
	•	Wait for human input before proceeding.

✅ Don’t duplicate. Group related experiments only when:
	•	They are intentional variants (e.g., only changing mask type)
	•	You explicitly mention each sub-condition under one heading

✅ Use terminology from pause-doc.pdf.

⸻

✅ Final Deliverable
	•	File format: .md (Markdown)
	•	Filename: experiment_details_report.md
	•	Sort: Strictly by Exp-{x}
	•	Include: Every experiment from the CSV

⸻

🧠 Example Template (With Continuation and Key Learnings)

## Exp-3: Learning Rate Sweep with OneCycle

**Description:**  
This experiment explores the effect of varying learning rates using a OneCycle scheduler during latent space training.

**Approach:**  
Tested LRs ranging from 1e-4 to 5e-3, using default losses and old affine transformation.

**Aim:**  
To identify an optimal LR for stable and efficient convergence in Stage 1.

**Key Learnings:**  
- LRs > 2e-3 led to unstable training.
- 1e-3 yielded the best perceptual results with stable loss curves.

**Status:**  
Success


⸻

✅ Summary

✅ Must Do	❌ Avoid
Include every experiment	Skipping failed runs
Sort strictly by Exp-{x}	Changing order arbitrarily
Ask if unsure (e.g., continuation)	Assuming status without proof
Use Markdown only	Mixing formats or styles
Include Key Learnings if known	Guessing outcomes
Avoid all mention of Latentsync	Mentioning Latentsync

---

Let me know if you'd like this saved to a downloadable `.md` file.