# Experiment Details Report

## Exp-14: Constant Learning Rate with New Affine Transformation

**Description:**  
This experiment explores constant learning rate scheduling with new affine transformation on Stage 2 pixel space training. Two runs were conducted with different checkpoint resumption strategies.

**Approach:**  
Used constant learning rate scheduler with new affine face templates, training for 6000-12000 steps on Stage 2 configuration.

**Aim:**  
To establish baseline performance with constant learning rate and evaluate checkpoint resumption effectiveness.

**Status:**  
Success

---

## Exp-15: Extended Training with VFHQ Dataset

**Description:**  
Extended training of Exp-14 baseline using VFHQ dataset for 18000 steps to assess longer training duration impact.

**Approach:**  
Resumed from Exp-14 checkpoint and extended training with VFHQ dataset using same configuration parameters.

**Aim:**  
To evaluate the benefits of extended training duration on model performance and temporal coherency.

**Status:**  
Success

---

## Exp-16: Motion Module Integration

**Description:**  
First experiment to integrate motion modules into the UNet architecture for improved temporal modeling.

**Approach:**  
Introduced motion module components while maintaining Stage 2 pixel space training configuration.

**Aim:**  
To enhance temporal coherency through dedicated motion modeling components.

**Status:**  
Success

---

## Exp-17: Adulteration Testing with Old Affine

**Description:**  
Multiple runs testing adulteration techniques with old affine transformation. Includes continuation experiments with different training durations.

**Approach:**  
Tested adulteration probability and weight parameters while using HDTF dataset for training.

**Aim:**  
To evaluate robustness of the model against input adulterations and assess old vs new affine performance.

**Status:**  
Success (main runs), Continued from Exp-16

---

## Exp-18: Stage 2 Training with New Affine

**Description:**  
Stage 2 training experiment using new affine transformation, building upon Exp-17 results.

**Approach:**  
Resumed from Exp-17 checkpoint and applied new affine transformation for 12000 training steps.

**Aim:**  
To compare new affine transformation performance against old affine baseline.

**Status:**  
Success

---

## Exp-19: VFHQ Dataset Integration

**Description:**  
Attempted integration of VFHQ dataset with baseline Exp-14 configuration.

**Approach:**  
Applied VFHQ dataset to established baseline configuration for dataset comparison.

**Aim:**  
To evaluate VFHQ dataset effectiveness compared to HDTF dataset.

**Status:**  
Crashed

---

## Exp-20: No Adulteration with New Affine

**Description:**  
Clean training run without adulteration techniques, using new affine transformation and HDTF dataset.

**Approach:**  
Disabled adulteration mechanisms while maintaining new affine transformation and motion modules.

**Aim:**  
To establish clean baseline performance without data augmentation interference.

**Status:**  
Success

---

## Exp-21: No Adulteration Validation

**Description:**  
Multiple validation runs of no-adulteration approach with new affine transformation.

**Approach:**  
Repeated Exp-20 configuration to validate reproducibility and stability.

**Aim:**  
To confirm consistency of no-adulteration training approach.

**Status:**  
Success (main run), Failed (validation run)

---

## Exp-22: Stage 1 Training with Adulteration

**Description:**  
Stage 1 latent space training experiment incorporating adulteration techniques with new affine transformation.

**Approach:**  
Conducted latent space training with reduced loss weights and adulteration probability of 0.5.

**Aim:**  
To establish Stage 1 foundation with robust adulteration handling for subsequent Stage 2 training.

**Status:**  
Success (main run), Crashed (rerun)

---

## Exp-23: Stage 2 with Adulteration Foundation

**Description:**  
Stage 2 pixel space training building upon Exp-22 Stage 1 results with adulteration techniques.

**Approach:**  
Resumed from Exp-22 checkpoint and applied full Stage 2 loss configuration with motion modules.

**Aim:**  
To complete two-stage training pipeline with adulteration robustness.

**Status:**  
Success

---

## Exp-24: Combined Stage Training

**Description:**  
Experiment combining both training stages with continuation from previous checkpoints.

**Approach:**  
Extended training from Exp-23 results with combined stage approach for 18000 steps.

**Aim:**  
To optimize end-to-end training pipeline through combined stage methodology.

**Status:**  
Success (continuation), Crashed (initial run)

---

## Exp-25: Stage 2 with SyncNet Integration

**Description:**  
Integration of SyncNet loss with Stage 2 training using reduced batch size for memory optimization.

**Approach:**  
Applied SyncNet loss weight of 0.05 with batch size reduction to 2 for computational efficiency.

**Aim:**  
To improve audio-visual synchronization through dedicated SyncNet loss integration.

**Status:**  
Crashed

---

## Exp-26: Fine-tuned SyncNet Integration

**Description:**  
Multiple attempts at integrating fine-tuned SyncNet model with varying loss configurations.

**Approach:**  
Tested different SyncNet loss weights (0.05 and 1.0) with fine-tuned SyncNet model.

**Aim:**  
To optimize SyncNet integration for improved synchronization performance.

**Status:**  
Success (main run), Crashed (extended run)

---

## Exp-27: Stage 2 SyncNet from Stage 1

**Description:**  
Stage 2 training with SyncNet integration starting from Stage 1 checkpoint.

**Approach:**  
Applied SyncNet loss from Stage 1 foundation with reduced batch size for stability.

**Aim:**  
To evaluate SyncNet effectiveness when applied from Stage 1 training foundation.

**Status:**  
Success

---

## Exp-28: Original SyncNet Integration

**Description:**  
Integration of original SyncNet model with reduced loss weight for stability.

**Approach:**  
Applied original SyncNet with loss weight of 0.01 to prevent training instability.

**Aim:**  
To compare original SyncNet performance against fine-tuned variants.

**Status:**  
Success (main run), Crashed (alternative run)

---

## Exp-29: Batch Size Optimization

**Description:**  
Optimization experiment focusing on batch size effects with fine-tuned SyncNet integration.

**Approach:**  
Maintained SyncNet configuration while testing batch size impact on training stability.

**Aim:**  
To optimize batch size for stable SyncNet training without memory constraints.

**Status:**  
Success (main run), Failed (initial attempt)

---

## Exp-30: Loss Weight Optimization

**Description:**  
Fine-tuning of SyncNet and perceptual loss weights with batch size optimization.

**Approach:**  
Reduced SyncNet loss to 0.005 and perceptual loss to 0.5 for balanced training.

**Aim:**  
To achieve optimal balance between synchronization and perceptual quality.

**Status:**  
Success

---

## Exp-33: Original SyncNet Validation

**Description:**  
Multiple validation attempts of original SyncNet integration with Exp-23 baseline.

**Approach:**  
Repeated original SyncNet configuration to validate training stability.

**Aim:**  
To confirm reproducibility of original SyncNet integration approach.

**Status:**  
Failed (all attempts)

---

## Exp-34: Stage 1 Batch Size Scaling

**Description:**  
Stage 1 training experiments with various batch sizes to optimize latent space training efficiency.

**Approach:**  
Tested batch sizes from 4 to 32 with different GPU configurations and training durations.

**Aim:**  
To determine optimal batch size for Stage 1 training efficiency and stability.

**Status:**  
Success (final run), Failed/Crashed (scaling attempts), Continued from previous runs

---

## Exp-35: Stage 2 without SyncNet Loss

**Description:**  
Stage 2 training experiment with SyncNet loss disabled to isolate other loss components.

**Approach:**  
Resumed from Exp-34 Stage 1 checkpoint with SyncNet loss weight set to 0.

**Aim:**  
To evaluate Stage 2 performance without SyncNet influence and isolate other loss contributions.

**Status:**  
Success

---

## Exp-36: Stage 2 with Minimal SyncNet

**Description:**  
Stage 2 training with minimal SyncNet loss weight for subtle synchronization guidance.

**Approach:**  
Applied SyncNet loss weight of 0.01 to provide minimal synchronization guidance.

**Aim:**  
To find optimal minimal SyncNet contribution without overwhelming other loss components.

**Status:**  
Success

---

## Exp-37: Extended No-SyncNet Training

**Description:**  
Extended Stage 2 training without SyncNet loss, continuing from Exp-35 results.

**Approach:**  
Extended training duration to 7000 steps while maintaining zero SyncNet loss weight.

**Aim:**  
To evaluate extended training benefits without SyncNet loss interference.

**Status:**  
Success

---

## Exp-38: Combined Stage with Minimal SyncNet

**Description:**  
Combined stage training approach with minimal SyncNet loss integration.

**Approach:**  
Applied combined stage methodology with SyncNet loss weight of 0.01 for balanced training.

**Aim:**  
To optimize combined stage training with subtle synchronization guidance.

**Status:**  
Success

---

## Exp-39: Stage 1 Non-Contiguous Training

**Description:**  
Stage 1 training with non-contiguous data sampling and continuation experiments.

**Approach:**  
Applied non-contiguous sampling strategy with multiple continuation runs for extended training.

**Aim:**  
To improve Stage 1 training robustness through non-contiguous data sampling.

**Status:**  
Success, Continued from previous runs

---

## Exp-40: Stage 2 Non-Contiguous Foundation

**Description:**  
Stage 2 training building upon Exp-39 non-contiguous Stage 1 foundation with multiple continuations.

**Approach:**  
Progressive training extensions from 9000 to 11000 steps using non-contiguous foundation.

**Aim:**  
To complete two-stage pipeline with non-contiguous training benefits and extended optimization.

**Status:**  
Success, Continued from Exp-39

---

## SL-01: SyncNet Loss Optimization

**Description:**  
Specialized experiments focusing on SyncNet loss optimization with fine-tuned models.

**Approach:**  
Applied optimized SyncNet loss weights with fine-tuned SyncNet model for enhanced synchronization.

**Aim:**  
To achieve optimal SyncNet integration for superior audio-visual synchronization.

**Status:**  
Success (both runs)

---

## SL-05: Alternative SyncNet Configuration

**Description:**  
Alternative SyncNet configuration experiment with increased loss weight.

**Approach:**  
Applied higher SyncNet loss weight of 0.05 for stronger synchronization enforcement.

**Aim:**  
To evaluate stronger SyncNet influence on training dynamics and final performance.

**Status:**  
Crashed
